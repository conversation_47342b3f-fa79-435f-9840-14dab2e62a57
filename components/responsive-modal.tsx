import React from "react";
import { useMedia } from "react-use";
import { Dialog, DialogContent } from "./ui/dialog";
import { Drawer, DrawerContent } from "./ui/drawer";
import { DialogTitle } from "@radix-ui/react-dialog";

interface ResponsiveModalProps {
  children: React.ReactNode;
  open: boolean;
  onOpenChange: (open: boolean) => void;
  isNormal?: boolean;
}

export const ResponsiveModal = ({
  children,
  open,
  onOpenChange,
  isNormal = true, 
}: ResponsiveModalProps) => {
  const media = useMedia("(min-width: 1024px)", false);

  if (media) {
    return (
      <Dialog open={open} onOpenChange={onOpenChange}>
        <DialogContent 
            className={`w-full sm:max-w-lg ${isNormal ? 'md:max-w-lg' : 'md:max-w-5xl'} p-0 border-none overflow-y-auto hide-scrollbar max-h-[85vh]`}>
          <DialogTitle></DialogTitle>
          {children}
        </DialogContent>
      </Dialog>
    );
  }

  return (
    <Drawer open={open} onOpenChange={onOpenChange}>
      <DrawerContent>
        <div className="overflow-y-auto hide-scrollbar max-h-[85vh]">
          {children}
        </div>
      </DrawerContent>
    </Drawer>
  );
};