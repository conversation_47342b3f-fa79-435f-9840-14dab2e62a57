"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { Purchases, FetchPurchaseRequest, FetchPurchaseResponse } from "@/features/purchases/types/purchases";
import { Filter, Plus, Trash, ShoppingBag } from "lucide-react";

import { useEffect, useState } from "react";
import { Badge } from "@/components/ui/badge";
import { Row } from "@tanstack/react-table";
import { PageHeader } from "@/components/page-header";
import { PurchaseDataTable } from "@/features/purchases/components/purchase-table";

export default function Page() {
  const [selectedRows, setSelectedRows] = useState<Row<Purchases>[]>([]);
  const [response, setResponse] = useState<FetchPurchaseResponse | null>(null);
  const [search, setSearch] = useState<string>("");
  const [filters, setFilters] = useState<FetchPurchaseRequest>({
    organization_id: localStorage.getItem("current_organization")!,
  });

  useEffect(() => {
    setFilters((prev) => ({
      ...prev,
      query: search,
    }));
  }, [search]);

  return (
    <div className="space-y-6">
      <PageHeader
        title="Purchase Management"
        description="Track and manage your purchase orders and supplier transactions"
        icon={ShoppingBag}
      />

      <Card>
        <CardHeader className="gap-y-2 flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center gap-2">
              <ShoppingBag className="h-5 w-5" />
              Purchases List
            </CardTitle>
            <CardDescription className="text-sm">
              {response?.total ?? 0} in total
            </CardDescription>
          </div>
          <Button
            onClick={() => {}}
            size="sm"
            className="mt-4"
          >
            <Plus className="size-4 mr-2" />
            Add New
          </Button>
        </CardHeader>

        <CardContent>
          <div className="mb-2 flex justify-start items-stretch gap-x-2">
            <input
              className="border focus:outline-none px-2 py-1 text-xs rounded-lg w-80"
              placeholder="Search purchases..."
              value={search}
              onChange={(e) => setSearch(e.target.value)}
            />
            <div className="flex-1"></div>

            <Button
              size="sm"
              variant="destructive"
              disabled={selectedRows.length == 0}
            >
              <Trash /> Bulk Delete{" "}
              {selectedRows.length > 0 && (
                <Badge variant="secondary">{selectedRows.length}</Badge>
              )}
            </Button>
            <Popover>
              <PopoverTrigger asChild>
                <Button size="sm" variant="secondary">
                  <Filter />
                </Button>
              </PopoverTrigger>

              <PopoverContent side="left" className="w-80">
                Filters
              </PopoverContent>
            </Popover>
          </div>
          <PurchaseDataTable
            onRowSelectedChanged={setSelectedRows}
            onDataChange={(res: FetchPurchaseResponse | null) => res && setResponse(res)}
            filters={filters}
            organization_id={localStorage.getItem("current_organization")!}
          />
        </CardContent>
      </Card>
    </div>
  );
}