"use client";

import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { SalesTable } from "@/features/sales/components/sales-table";
import { FetchSalesRequest, FetchSalesResponse, Sale } from "@/features/sales/types/sales";
import { Row } from "@tanstack/react-table";
import { Filter, Plus, Trash, TrendingUp } from "lucide-react";
import { PageHeader } from "@/components/page-header";
import { useParams } from "next/navigation";
import { useEffect, useState } from "react";

export default function Page() {
 
 const [selectedRows, setSelectedRows] = useState<Row<Sale>[]>([]);
 const [response, setResponse] = useState<FetchSalesResponse | null>(
   null
  );


  const [search, setSearch] = useState<string>("");
  const [filters, setFilters] = useState<FetchSalesRequest>({
    organization_id: localStorage.getItem("current_organization")!,
  });


  useEffect(() => {
    setFilters((prev) => ({
      ...prev,
      search: search,
    }));
  }, [search]);
 
  return (
    <div className="space-y-6">
      <PageHeader
        title="Sales Management"
        description="Track and manage your sales transactions"
        icon={TrendingUp}
      />

      <Card>
        <CardHeader className="gap-y-2 flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center gap-2">
              <TrendingUp className="h-5 w-5" />
              Sales List
            </CardTitle>
            <CardDescription className="text-sm">
              {response?.total ?? 0} in total
            </CardDescription>
          </div>
          <Button
            onClick={() => {}}
            size="sm"
            className="mt-4"
          >
            <Plus className="size-4 mr-2" />
            Add New
          </Button>
        </CardHeader>

        <CardContent>
          <div className="mb-2 flex justify-start items-stretch gap-x-2">
            <input
              className="border focus:outline-none px-2 py-1 text-xs rounded-lg w-80"
              placeholder="Search"
              value={search}
              onChange={(e) => setSearch(e.target.value)}
            />
            <div className="flex-1"></div>

            <Button
              size="sm"
              variant="destructive"
              disabled={selectedRows.length == 0}
            >
              <Trash /> Bulk Delete{" "}
              {selectedRows.length > 0 && (
                <Badge variant="secondary">{selectedRows.length}</Badge>
              )}
            </Button>
            <Popover>
              <PopoverTrigger asChild>
                <Button size="sm" variant="secondary">
                  <Filter />
                </Button>
              </PopoverTrigger>

              <PopoverContent side="left" className="w-80">
                Filter
              </PopoverContent>
            </Popover>
          </div>
          <SalesTable
            onRowSelectedChanged={setSelectedRows}
            onDataChange={(res) => res && setResponse(res)}
            filters={filters}
          />
        </CardContent>
      </Card>

    </div>
  );
}