"use client";

import { useEffect, useState } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { Input } from "@/components/ui/input";
import { motion } from "framer-motion";
import { Progress } from "@/components/ui/progress";
import { Skeleton } from "@/components/ui/skeleton";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { InventoryItemTable } from "@/features/inventories/components/inventory-table";
import { InventoryCategoryTable } from "@/features/inventory_category/components/inventory-categories-table";
import { Row } from "@tanstack/react-table";
import { 
  Download, 
  Filter, 
  Plus, 
  Trash, 
  Package, 
  FolderOpen, 
  Warehouse,
  AlertCircle,
  BarChart2,
  PackageCheck,
  PackageX,
  RefreshCw,
  Search,
  Sliders
} from "lucide-react";
import { FaFileExcel } from "react-icons/fa";
import { PageHeader } from "@/components/page-header";
import { FetchInventoryRequest, FetchInventoryResponse, InventoryItem } from "@/features/inventories/types/inventories";
import { FetchInventoryCategoryRequest, FetchInventoryCategoryResponse, InventoryCategory } from "@/features/inventory_category/types/inventoryCategories";

// Custom color palette
const COLORS = {
  primary: '#47b37c',
  primaryLight: '#c8ffd9',
  primaryDark: '#133330',
  accent: '#47b37c',
  warning: '#f59e0b',
  danger: '#ef4444',
  success: '#10b981'
};

// Mock data for inventory stats (replace with API calls)
const inventoryStats = {
  totalItems: 1245,
  lowStock: 42,
  outOfStock: 18,
  categories: 28,
  inventoryValue: 125430.75,
  inventoryHealth: 82, // percentage
};

export default function InventoriesPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [isRefreshing, setIsRefreshing] = useState(false);

  // Get tab from URL or default to "inventories"
  const currentTab = searchParams.get("tab") || "inventories";

  // Inventory state
  const [selectedInventoryRows, setSelectedInventoryRows] = useState<Row<InventoryItem>[]>([]);
  const [inventoryResponse, setInventoryResponse] = useState<FetchInventoryResponse | null>(null);
  const [inventorySearch, setInventorySearch] = useState<string>("");
  const [inventoryFilters, setInventoryFilters] = useState<FetchInventoryRequest>({
    organization_id: localStorage.getItem("current_organization")!,
  });

  // Categories state
  const [selectedCategoryRows, setSelectedCategoryRows] = useState<Row<InventoryCategory>[]>([]);
  const [categoryResponse, setCategoryResponse] = useState<FetchInventoryCategoryResponse | null>(null);
  const [categorySearch, setCategorySearch] = useState<string>("");
  const [categoryFilters, setCategoryFilters] = useState<FetchInventoryCategoryRequest>({
    organization_id: localStorage.getItem("current_organization")!,
  });

  const handleTabChange = (value: string) => {
    const params = new URLSearchParams(searchParams.toString());
    params.set("tab", value);
    router.push(`/inventories?${params.toString()}`);
  };

  const handleRefresh = () => {
    setIsRefreshing(true);
    // Simulate API refresh
    setTimeout(() => setIsRefreshing(false), 1000);
  };

  useEffect(() => {
    setInventoryFilters((prev) => ({
      ...prev,
      query: inventorySearch,
    }));
  }, [inventorySearch]);

  useEffect(() => {
    setCategoryFilters((prev) => ({
      ...prev,
      query: categorySearch,
    }));
  }, [categorySearch]);

  return (
    <div className="space-y-6">
      <PageHeader
        title="Inventory Management"
        description="Track, manage and optimize your inventory"
        icon={Warehouse}
        gradientText
        badge="Live"
      />

      {/* Inventory Overview Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <motion.div whileHover={{ y: -2 }}>
          <Card className="border-[var(--primary-light)]/50 hover:border-[var(--primary)]/40 transition-colors">
            <CardHeader className="flex flex-row items-center justify-between pb-2">
              <CardTitle className="text-sm font-medium">Total Items</CardTitle>
              <Package className="h-4 w-4" style={{ color: COLORS.primary }} />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{inventoryStats.totalItems}</div>
              <p className="text-xs text-muted-foreground">+12% from last month</p>
            </CardContent>
          </Card>
        </motion.div>

        <motion.div whileHover={{ y: -2 }}>
          <Card className="border-warning/20 hover:border-warning/40 transition-colors">
            <CardHeader className="flex flex-row items-center justify-between pb-2">
              <CardTitle className="text-sm font-medium">Low Stock</CardTitle>
              <AlertCircle className="h-4 w-4 text-warning" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{inventoryStats.lowStock}</div>
              <p className="text-xs text-muted-foreground">Items below threshold</p>
            </CardContent>
          </Card>
        </motion.div>

        <motion.div whileHover={{ y: -2 }}>
          <Card className="border-danger/20 hover:border-danger/40 transition-colors">
            <CardHeader className="flex flex-row items-center justify-between pb-2">
              <CardTitle className="text-sm font-medium">Out of Stock</CardTitle>
              <PackageX className="h-4 w-4 text-danger" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{inventoryStats.outOfStock}</div>
              <p className="text-xs text-muted-foreground">Urgent reorder needed</p>
            </CardContent>
          </Card>
        </motion.div>

        <motion.div whileHover={{ y: -2 }}>
          <Card className="border-success/20 hover:border-success/40 transition-colors">
            <CardHeader className="flex flex-row items-center justify-between pb-2">
              <CardTitle className="text-sm font-medium">Inventory Value</CardTitle>
              <BarChart2 className="h-4 w-4 text-success" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">${inventoryStats.inventoryValue.toLocaleString()}</div>
              <p className="text-xs text-muted-foreground">Total current value</p>
            </CardContent>
          </Card>
        </motion.div>
      </div>

      {/* Inventory Health */}
      <Card className="border-[var(--primary)]/20">
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle>Inventory Health</CardTitle>
              <CardDescription>Overall status of your inventory</CardDescription>
            </div>
            <Button variant="ghost" size="sm" onClick={handleRefresh} disabled={isRefreshing}>
              <RefreshCw className={`h-4 w-4 mr-2 ${isRefreshing ? "animate-spin" : ""}`} />
              Refresh
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium">Stock Coverage</span>
              <span className="text-sm font-medium">{inventoryStats.inventoryHealth}%</span>
            </div>
            <Progress 
              value={inventoryStats.inventoryHealth} 
              className="h-2" 
              style={{
                backgroundColor: COLORS.primaryLight,
                ['--primary' as keyof React.CSSProperties]: COLORS.primary,
              }}
            />
            <div className="flex justify-between text-xs text-muted-foreground">
              <span>Needs Attention</span>
              <span>Optimal</span>
            </div>
          </div>
        </CardContent>
        <CardFooter className="text-xs text-muted-foreground">
          <PackageCheck className="h-3 w-3 mr-1 inline" />
          {inventoryStats.categories} active categories
        </CardFooter>
      </Card>

      {/* Main Inventory Content */}
      <Tabs value={currentTab} onValueChange={handleTabChange} className="space-y-4">
        <motion.div
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3 }}
        >
          <TabsList className="grid w-full grid-cols-2 bg-muted/50 p-1.5 h-auto">
            <TabsTrigger 
              value="inventories" 
              className="flex items-center gap-2 py-2 data-[state=active]:bg-background data-[state=active]:shadow-sm"
              style={{
                color: currentTab === 'inventories' ? COLORS.primaryDark : 'inherit',
              }}
            >
              <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
                <Package className="h-4 w-4" />
              </motion.div>
              Inventory Items
              {inventoryResponse?.total && (
                <Badge variant="secondary" className="ml-1">
                  {inventoryResponse.total}
                </Badge>
              )}
            </TabsTrigger>
            <TabsTrigger 
              value="categories" 
              className="flex items-center gap-2 py-2 data-[state=active]:bg-background data-[state=active]:shadow-sm"
              style={{
                color: currentTab === 'categories' ? COLORS.primaryDark : 'inherit',
              }}
            >
              <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
                <FolderOpen className="h-4 w-4" />
              </motion.div>
              Categories
              {categoryResponse?.total && (
                <Badge variant="secondary" className="ml-1">
                  {categoryResponse.total}
                </Badge>
              )}
            </TabsTrigger>
          </TabsList>
        </motion.div>

        {/* Inventory Items Tab */}
        <TabsContent value="inventories" className="space-y-4">
          <motion.div
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3, delay: 0.1 }}
          >
            <Card className="border-[var(--primary)]/20 hover:border-[var(--primary)]/30 transition-colors duration-300 shadow-sm hover:shadow-md">
              <CardHeader className="p-6 pb-2 sm:p-6 sm:pb-2">
                <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
                  <div>
                    <CardTitle className="flex items-center gap-2 text-lg font-semibold">
                      <motion.div
                        whileHover={{ rotate: 5 }}
                        className="p-1.5 rounded-lg bg-[var(--primary-light)]"
                      >
                        <Package className="h-5 w-5" style={{ color: COLORS.primaryDark }} />
                      </motion.div>
                      Inventory Items
                    </CardTitle>
                    <CardDescription className="text-sm mt-1">
                      Manage all your inventory items in one place
                    </CardDescription>
                  </div>

                  <div className="flex flex-wrap gap-2">
                    <TooltipProvider>
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <Button
                            variant="outline"
                            size="sm"
                            className="gap-2 bg-[var(--primary-light)] hover:bg-[var(--primary-light)]/80 border-[var(--primary)]/20"
                          >
                            <Download className="size-4" />
                            Export
                          </Button>
                        </TooltipTrigger>
                        <TooltipContent>
                          <p>Export to CSV, Excel, or PDF</p>
                        </TooltipContent>
                      </Tooltip>
                    </TooltipProvider>

                    <TooltipProvider>
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <Button
                            variant="outline"
                            size="sm"
                            className="gap-2 bg-[var(--primary-light)] hover:bg-[var(--primary-light)]/80 border-[var(--primary)]/20"
                          >
                            <FaFileExcel className="size-4" />
                            Import
                          </Button>
                        </TooltipTrigger>
                        <TooltipContent>
                          <p>Bulk import from spreadsheet</p>
                        </TooltipContent>
                      </Tooltip>
                    </TooltipProvider>

                    <Button
                      size="sm"
                      className="gap-2 shadow-sm hover:shadow-md transition-shadow"
                      style={{
                        background: `linear-gradient(to right, ${COLORS.primary}, ${COLORS.primaryDark})`,
                        color: 'white'
                      }}
                    >
                      <Plus className="size-4" />
                      New Item
                    </Button>
                  </div>
                </div>
              </CardHeader>

              <CardContent className="p-6 pt-2">
                <div className="mb-4 flex flex-col sm:flex-row gap-3 items-start sm:items-center">
                  <div className="relative w-full sm:w-96">
                    <Search className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                    <Input
                      className="w-full pl-9 focus-visible:ring-[var(--primary)]/50"
                      placeholder="Search by SKU, name, or description..."
                      value={inventorySearch}
                      onChange={(e) => setInventorySearch(e.target.value)}
                    />
                  </div>
                  
                  <div className="flex gap-2 ml-auto">
                    <Button
                      size="sm"
                      variant="destructive"
                      disabled={selectedInventoryRows.length == 0}
                      className="gap-2"
                    >
                      <Trash className="size-4" />
                      Delete
                      {selectedInventoryRows.length > 0 && (
                        <Badge variant="secondary" className="px-1.5 py-0.5">
                          {selectedInventoryRows.length}
                        </Badge>
                      )}
                    </Button>
                    
                    <Popover>
                      <PopoverTrigger asChild>
                        <Button 
                          size="sm" 
                          variant="outline"
                          className="gap-2 border-[var(--primary)]/20 hover:border-[var(--primary)]/30"
                        >
                          <Sliders className="size-4" />
                          Filters
                        </Button>
                      </PopoverTrigger>
                      <PopoverContent side="left" className="w-80 p-4">
                        <div className="space-y-4">
                          <h4 className="font-medium leading-none">Advanced Filters</h4>
                          <div className="grid gap-2">
                            <div>
                              <label className="text-sm font-medium">Stock Status</label>
                              <div className="grid grid-cols-2 gap-2 mt-1">
                                <Button variant="outline" size="sm">In Stock</Button>
                                <Button variant="outline" size="sm">Low Stock</Button>
                                <Button variant="outline" size="sm">Out of Stock</Button>
                                <Button variant="outline" size="sm">Discontinued</Button>
                              </div>
                            </div>
                            <div>
                              <label className="text-sm font-medium">Price Range</label>
                              <div className="flex gap-2 mt-1">
                                <Input placeholder="Min" className="h-8" />
                                <Input placeholder="Max" className="h-8" />
                              </div>
                            </div>
                          </div>
                        </div>
                      </PopoverContent>
                    </Popover>
                  </div>
                </div>
                <InventoryItemTable
                  onRowSelectedChanged={setSelectedInventoryRows}
                  onDataChange={(res) => res && setInventoryResponse(res)}
                  filters={inventoryFilters}
                />
              </CardContent>
            </Card>
          </motion.div>
        </TabsContent>

        {/* Inventory Categories Tab */}
        <TabsContent value="categories" className="space-y-4">
          <motion.div
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3, delay: 0.1 }}
          >
            <Card className="border-[var(--primary)]/20 hover:border-[var(--primary)]/30 transition-colors duration-300 shadow-sm hover:shadow-md">
              <CardHeader className="p-6 pb-2 sm:p-6 sm:pb-2">
                <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
                  <div>
                    <CardTitle className="flex items-center gap-2 text-lg font-semibold">
                      <motion.div
                        whileHover={{ rotate: 5 }}
                        className="p-1.5 rounded-lg bg-[var(--primary-light)]"
                      >
                        <FolderOpen className="h-5 w-5" style={{ color: COLORS.primaryDark }} />
                      </motion.div>
                      Inventory Categories
                    </CardTitle>
                    <CardDescription className="text-sm mt-1">
                      Organize your inventory with categories
                    </CardDescription>
                  </div>

                  <Button
                    size="sm"
                    className="gap-2 shadow-sm hover:shadow-md transition-shadow"
                    style={{
                      background: `linear-gradient(to right, ${COLORS.primary}, ${COLORS.primaryDark})`,
                      color: 'white'
                    }}
                  >
                    <Plus className="size-4" />
                    New Category
                  </Button>
                </div>
              </CardHeader>

              <CardContent className="p-6 pt-2">
                <div className="mb-4 flex flex-col sm:flex-row gap-3 items-start sm:items-center">
                  <div className="relative w-full sm:w-96">
                    <Search className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                    <Input
                      className="w-full pl-9 focus-visible:ring-[var(--primary)]/50"
                      placeholder="Search categories..."
                      value={categorySearch}
                      onChange={(e) => setCategorySearch(e.target.value)}
                    />
                  </div>
                  
                  <div className="flex gap-2 ml-auto">
                    <Button
                      size="sm"
                      variant="destructive"
                      disabled={selectedCategoryRows.length == 0}
                      className="gap-2"
                    >
                      <Trash className="size-4" />
                      Delete
                      {selectedCategoryRows.length > 0 && (
                        <Badge variant="secondary" className="px-1.5 py-0.5">
                          {selectedCategoryRows.length}
                        </Badge>
                      )}
                    </Button>
                    
                    <Popover>
                      <PopoverTrigger asChild>
                        <Button 
                          size="sm" 
                          variant="outline"
                          className="gap-2 border-[var(--primary)]/20 hover:border-[var(--primary)]/30"
                        >
                          <Sliders className="size-4" />
                          Filters
                        </Button>
                      </PopoverTrigger>
                      <PopoverContent side="left" className="w-80 p-4">
                        <div className="space-y-4">
                          <h4 className="font-medium leading-none">Category Filters</h4>
                          <div className="grid gap-2">
                            <div>
                              <label className="text-sm font-medium">Status</label>
                              <div className="grid grid-cols-2 gap-2 mt-1">
                                <Button variant="outline" size="sm">Active</Button>
                                <Button variant="outline" size="sm">Archived</Button>
                              </div>
                            </div>
                            <div>
                              <label className="text-sm font-medium">Items Count</label>
                              <div className="flex gap-2 mt-1">
                                <Input placeholder="Min items" className="h-8" />
                                <Input placeholder="Max items" className="h-8" />
                              </div>
                            </div>
                          </div>
                        </div>
                      </PopoverContent>
                    </Popover>
                  </div>
                </div>
                <InventoryCategoryTable
                  onRowSelectedChanged={setSelectedCategoryRows}
                  onDataChange={(res) => res && setCategoryResponse(res)}
                  filters={categoryFilters}
                />
              </CardContent>
            </Card>
          </motion.div>
        </TabsContent>
      </Tabs>
    </div>
  );
}