"use client";

import { useState } from "react";
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, Card<PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Separator } from "@/components/ui/separator";
import { Badge } from "@/components/ui/badge";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { toast } from "sonner";
import { Smartphone, CreditCard, Phone, Mail, HelpCircle } from "lucide-react";

const mobileMoneySchema = z.object({
  phone: z.string().min(10, "Phone number must be at least 10 digits"),
  amount: z.number().min(1000, "Minimum amount is 1,000 TZS").max(1000000, "Maximum amount is 1,000,000 TZS"),
});

type MobileMoneyFormData = z.infer<typeof mobileMoneySchema>;

interface TokenPurchaseFormProps {
  onCancel?: () => void;
}

export const TokenPurchaseForm = ({ onCancel }: TokenPurchaseFormProps) => {
  const [activeTab, setActiveTab] = useState("mobile-money");
  const [isLoading, setIsLoading] = useState(false);

  const form = useForm<MobileMoneyFormData>({
    resolver: zodResolver(mobileMoneySchema),
    defaultValues: {
      phone: "",
      amount: 1000,
    },
  });

  const watchedAmount = form.watch("amount");
  const tokenAmount = Math.floor(watchedAmount / 6.3);

  const onSubmit = async (values: MobileMoneyFormData) => {
    setIsLoading(true);
    try {
      // Simulate API call
      console.log(values)
      await new Promise(resolve => setTimeout(resolve, 2000));
      toast.success("Payment request sent! Check your phone for the prompt.");
      onCancel?.();
    } catch (e) {
      console.log(e)
      toast.error("Failed to process payment. Please try again.");
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Card className="w-full h-full border-none shadow-none">
      <CardHeader className="flex p-7">
        <CardTitle className="text-xl font-bold flex items-center gap-2">
          <CreditCard className="h-5 w-5" />
          Add Tokens
        </CardTitle>
      </CardHeader>
      <div className="px-7">
        <Separator />
      </div>
      <CardContent className="p-7">
        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="mobile-money" className="flex items-center gap-2">
              <Smartphone className="h-4 w-4" />
              Mobile Money
            </TabsTrigger>
            <TabsTrigger value="lipa-number" className="flex items-center gap-2">
              <Phone className="h-4 w-4" />
              Lipa Number
            </TabsTrigger>
          </TabsList>

          {/* Mobile Money Tab */}
          <TabsContent value="mobile-money" className="space-y-6 mt-6">
            <Form {...form}>
              <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
                <div className="space-y-4">
                  <FormField
                    control={form.control}
                    name="phone"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Phone Number</FormLabel>
                        <FormControl>
                          <Input
                            placeholder="e.g., +255 748 123 456"
                            {...field}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="amount"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Amount (TZS)</FormLabel>
                        <FormControl>
                          <Input
                            type="number"
                            min={1000}
                            max={1000000}
                            step={100}
                            {...field}
                            onChange={(e) => field.onChange(Number(e.target.value))}
                          />
                        </FormControl>
                        <div className="text-xs text-muted-foreground">
                          Limits: 1,000 - 1,000,000 TZS
                        </div>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  {/* Token Conversion Display */}
                  <div className="bg-muted/50 rounded-lg p-4 space-y-2">
                    <div className="flex justify-between items-center">
                      <span className="text-sm font-medium">You will receive:</span>
                      <Badge variant="secondary" className="text-base">
                        {tokenAmount} tokens
                      </Badge>
                    </div>
                    <div className="text-xs text-muted-foreground">
                      1 token = 6.3 TZS
                    </div>
                  </div>

                  {/* Payment Instructions */}
                  <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                    <div className="flex items-start gap-3">
                      <Smartphone className="h-5 w-5 text-blue-600 mt-0.5" />
                      <div>
                        <p className="text-sm font-medium text-blue-900">Payment Instructions</p>
                        <p className="text-xs text-blue-700 mt-1">
                          You will receive a prompt on your phone to authorize the payment
                        </p>
                      </div>
                    </div>
                  </div>
                </div>

                <Separator />

                <div className="flex items-center justify-between">
                  <Button
                    type="button"
                    variant="secondary"
                    onClick={onCancel}
                    disabled={isLoading}
                  >
                    Cancel
                  </Button>
                  <Button type="submit" disabled={isLoading}>
                    {isLoading ? "Processing..." : "Purchase"}
                  </Button>
                </div>
              </form>
            </Form>
          </TabsContent>

          {/* Lipa Number Tab */}
          <TabsContent value="lipa-number" className="space-y-6 mt-6">
            <div className="space-y-6">
              {/* Business Information */}
              <div className="bg-green-50 border border-green-200 rounded-lg p-6">
                <h3 className="font-semibold text-green-900 mb-4 flex items-center gap-2">
                  {/* <CreditCard className="h-5 w-5" /> */}
                  Vodacom Lipa Number
                </h3>
                <div className="space-y-3">
                  <div>
                    <Label className="text-sm font-medium text-green-800">Business Name</Label>
                    <p className="text-lg font-bold text-green-900">GALATECH LIMITED</p>
                  </div>
                  <div>
                    <Label className="text-sm font-medium text-green-800">Lipa Number</Label>
                    <p className="text-2xl font-bold text-green-900 tracking-wider">58237033</p>
                  </div>
                </div>
              </div>

              {/* Step-by-step Instructions */}
              <div className="space-y-4">
                <h3 className="font-semibold text-gray-900">How to Pay via Lipa Namba</h3>
                <div className="space-y-3">
                  <div className="flex gap-3">
                    <div className="flex-shrink-0 w-6 h-6 bg-blue-600 text-white rounded-full flex items-center justify-center text-sm font-bold">
                      1
                    </div>
                    <p className="text-sm text-gray-700">Dial *150*00#</p>
                  </div>
                  <div className="flex gap-3">
                    <div className="flex-shrink-0 w-6 h-6 bg-blue-600 text-white rounded-full flex items-center justify-center text-sm font-bold">
                      2
                    </div>
                    <p className="text-sm text-gray-700">Select 4 for Lipa Namba</p>
                  </div>
                  <div className="flex gap-3">
                    <div className="flex-shrink-0 w-6 h-6 bg-blue-600 text-white rounded-full flex items-center justify-center text-sm font-bold">
                      3
                    </div>
                    <p className="text-sm text-gray-700">Enter Lipa Number: <strong>58237033</strong></p>
                  </div>
                  <div className="flex gap-3">
                    <div className="flex-shrink-0 w-6 h-6 bg-blue-600 text-white rounded-full flex items-center justify-center text-sm font-bold">
                      4
                    </div>
                    <p className="text-sm text-gray-700">Enter the amount you want to pay</p>
                  </div>
                  <div className="flex gap-3">
                    <div className="flex-shrink-0 w-6 h-6 bg-blue-600 text-white rounded-full flex items-center justify-center text-sm font-bold">
                      5
                    </div>
                    <p className="text-sm text-gray-700">Enter your PIN to confirm</p>
                  </div>
                </div>
              </div>

              {/* Need Help Section */}
              <div className="bg-gray-50 border border-gray-200 rounded-lg p-6">
                <h3 className="font-semibold text-gray-900 mb-4 flex items-center gap-2">
                  <HelpCircle className="h-5 w-5" />
                  Need Help?
                </h3>
                <div className="space-y-3">
                  <div className="flex items-center gap-3">
                    <Phone className="h-4 w-4 text-gray-600" />
                    <div>
                      <Label className="text-sm font-medium">Phone</Label>
                      <p className="text-sm text-gray-700">+255 748 884 488</p>
                    </div>
                  </div>
                  <div className="flex items-center gap-3">
                    <Mail className="h-4 w-4 text-gray-600" />
                    <div>
                      <Label className="text-sm font-medium">Email</Label>
                      <p className="text-sm text-gray-700"><EMAIL></p>
                    </div>
                  </div>
                </div>
              </div>

              <Separator />

              <div className="flex items-center justify-center">
                <Button variant="secondary" onClick={onCancel}>
                  Close
                </Button>
              </div>
            </div>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
};
