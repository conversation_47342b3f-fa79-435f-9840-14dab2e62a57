import axios from "axios";
import <PERSON>zi<PERSON><PERSON> from "@/lib/endpoints";
import { FetchSalesRequest } from "../types/sales";

export class Sales {
    private static token = localStorage.getItem("izi_token");

    public static async getAllSales(request: FetchSalesRequest) {
        try {
            const response = await axios.get(IziApi.sales, {
                params: request,
                headers: {
                    Authorization: `Bearer ${this.token}`,
                },
            });

            if (response.status === 200) {
                return response.data; 
            }
        } catch (error: any) {
            throw error.response.data; 
        }
    }
}
