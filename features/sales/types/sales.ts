import { User } from "@/features/auth/types/auth";
import { Organization } from "@/features/organizations/types/organization";
import { z } from "zod";
import { fetchSalesSchema } from "../schemas";
import { Customer } from "@/features/customers/server/Customer";


export interface Item {
    id: string;
    inventory_category_id: string | null;
    name: string;
    description: string;
    unit_type_id: string;
    is_manufactured: string;
    is_material: string;
    is_product: string;
    default_unit_id: string;
    reorder_level: string | null;
    in_stock: string;
    featured_image: string;
    deleted_at: string | null;
    created_at: string;
    updated_at: string;
    sale_price: string;
    inventory_item_sku: string;
    organization_id: string;
    product_id: string | null;
}

export interface StockItem {
    id: string;
    inv_item_id: string;
    source_id: string;
    source_type: string;
    inv_warehouse_id: string;
    quantity: string;
    unit_cost: string;
    in_stock: string;
    deleted_at: string | null;
    created_at: string;
    updated_at: string;
    unit_id: string;
    warehouse: {
        id: string;
        name: string;
        description: string;
        featured_image: string;
        deleted_at: string | null;
        created_at: string;
        updated_at: string;
        organization_id: string;
    };
}

export interface SaleItem {
    id: string;
    sale_id: string;
    inv_item_id: string;
    conf_unit_id: string;
    quantity: string;
    unit_price: string;
    deleted_at: string | null;
    created_at: string;
    updated_at: string;
    organization_id: string | null;
    item: Item;
    stock_items: StockItem[];
}

export interface Sale {
    id: string;

    code: string;
    description: string | null;
    total_amount: string;
    received_amount: string;
    return_amount: string;
    status: string;
    amount_credit: string;
    date: string;

    customer_id: string;
    customer: Customer;

    organization_id: string;
    organization: Organization;

    user_id: string | null;
    user: User;

    sale_items: SaleItem[];
    is_credit_sale: boolean;
    deleted_at: string | null;
    created_at: string;
    updated_at: string;
}

// REQUEST AND RESPONSE TYPES

// Fetch sales
export type FetchSalesRequest = z.infer<typeof fetchSalesSchema>;

export interface FetchSalesResponse {
  data: Sale[];
  total: number;
}

// Retrieve sales
export interface RetrieveSalesResponse {
    data: Sale;
  }

export interface ReturnSalesResponse {
    data: Sale;
  
}


  // Delete sale
export interface DeleteSaleResponse {
    data?: Sale;
  }