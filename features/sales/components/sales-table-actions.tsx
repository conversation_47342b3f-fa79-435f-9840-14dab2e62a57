import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {  Eye, Loader2, MoreHorizontal,TimerResetIcon,Trash } from "lucide-react";
import React from "react";
import { Sale } from "../types/sales";
import { useRouter, useParams } from "next/navigation";

type Props = {
  sale: Sale;
};

export const SaleTableActions = ({ sale }: Props) => {
    const { slug: organization_id } = useParams();
  
    const handleDelete = async () => {
      
        console.log("Sales deleted successfully");
       
    };

  const router = useRouter();
  return (
    <div className="flex items-center space-x-2"> 
     
      {/* <Button variant="ghost" onClick={() => edit.onOpen(sale)}>Edit</Button> */}
    <DropdownMenu>

      <DropdownMenuTrigger asChild>
        <Button variant="ghost" className="h-8 w-8 p-0">
          <span className="sr-only">Open menu</span>
          <MoreHorizontal />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end">
        <DropdownMenuLabel>Actions {sale.code}</DropdownMenuLabel>
        <DropdownMenuSeparator />
        <DropdownMenuGroup>
          <DropdownMenuItem className="flex" onClick={() => router.push(`/organizations/${organization_id}/sales/${sale.id}`)}>
            <Eye /> View
          </DropdownMenuItem>
          <DropdownMenuItem onClick={() => {}}>
            <TimerResetIcon /> Return
            {/* {returnSale.isPending && <Loader2 className="spin"/> } */}
          </DropdownMenuItem>
          <DropdownMenuSeparator />
            <DropdownMenuItem 
              onClick={handleDelete}
            >
              <Trash color="red" /> Delete 
            {/* {isPending && <Loader2 className="spin"/> } */}
            </DropdownMenuItem>        
          </DropdownMenuGroup>
      </DropdownMenuContent>
    </DropdownMenu>
    </div>
  );
};