import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Edit, Eye, Loader2, MoreHorizontal, Trash } from "lucide-react";
import React from "react";
import { useRouter } from "next/navigation";
import { useConfirm } from "@/hooks/use-confirm";
import { Supplier } from "../types/suppliers";
import { useSupplierFormModal } from "../hooks/use-supplier-form-modal";
import { useDeleteSupplier } from "../api/use-delete-supplier";

type Props = {
  supplier: Supplier;
  organization_id: string;
};

export const SupplierTableActions = ({ supplier }: Props) => {
  const router = useRouter();

    const { edit } = useSupplierFormModal();

    const { mutate, isPending } = useDeleteSupplier();
      
        const [DeletingDialog, confirmDelete] = useConfirm(
          "Delete Supplier",
          "This action cannot be undone",
          "ghost"
        );
      
        const handleDelete = async () => {
          const ok = await confirmDelete();
          if (!ok) return;
          mutate(supplier.id.toString(), {
            onSuccess: () => {
              console.log("Supplier deleted successfully");
            },
            onError: () => {
              console.error("Error deleting Supplier:", supplier.id);
            },
          });
        };
  
  return (
    <>
        
    <DeletingDialog />

    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="ghost" className="h-8 w-8 p-0">
          <span className="sr-only">Open menu</span>
          <MoreHorizontal />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end">
        <DropdownMenuLabel>Actions</DropdownMenuLabel>
        <DropdownMenuSeparator />
        <DropdownMenuGroup>
          <DropdownMenuItem className="flex" onClick={() => router.push(`/suppliers/${supplier.id}`)}>
            <Eye /> View
          </DropdownMenuItem>
          <DropdownMenuItem onClick={() => edit(supplier.id.toString())}>
            <Edit /> Edit
          </DropdownMenuItem>
          <DropdownMenuSeparator />
          
          <DropdownMenuItem 
            className="text-red-600 flex"
            disabled={isPending}
            onClick={handleDelete}
          ><Trash color="red" /> Delete 
          {isPending && <Loader2 className="spin" />}
          </DropdownMenuItem>
        </DropdownMenuGroup>
      </DropdownMenuContent>
    </DropdownMenu>

    </>

  );
};