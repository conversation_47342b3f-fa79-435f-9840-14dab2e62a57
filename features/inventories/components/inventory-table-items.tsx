import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Edit, Eye, Loader2, MoreHorizontal, Trash } from "lucide-react";
import React from "react";
import { InventoryItem } from "../types/inventories";

type Props = {
  inventoryItem: InventoryItem;
};

export const InventoryItemTableActions = ({ inventoryItem }: Props) => {


  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="ghost" className="h-8 w-8 p-0">
          <span className="sr-only">Open menu</span>
          <MoreHorizontal />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end">
        <DropdownMenuLabel>Actions </DropdownMenuLabel>
        <DropdownMenuSeparator />
        <DropdownMenuGroup>
          <DropdownMenuItem className="flex" onClick={() => {}}>
            <Eye /> View
          </DropdownMenuItem>
          <DropdownMenuItem onClick={() => {}}>
            <Edit /> Edit
          </DropdownMenuItem>
          <DropdownMenuSeparator />
            <DropdownMenuItem 
            //   onClick={() => {
            //     if (inventoryItem?.id) {
            //       deleteInventoryItem.mutate(inventoryItem.id.toString());
            //     }
            //   }}
            >
              <Trash color="red" /> Delete 
            {/* {deleteInventoryItem.isPending && <Loader2 className="spin"/> } */}
            </DropdownMenuItem>        
          </DropdownMenuGroup>
      </DropdownMenuContent>
    </DropdownMenu>
  );
};