import axios from "axios";
import <PERSON>zi<PERSON><PERSON> from "@/lib/endpoints";
import { FetchInventoryRequest } from "../types/inventories";

export class Inventories {
    private static token = localStorage.getItem("izi_token");

    public static async getAllInventories(request: FetchInventoryRequest) {
        try {
            const response = await axios.get(IziApi.inventories, {
                params: request,
                headers: {
                    Authorization: `Bearer ${this.token}`,
                },
            });

            if (response.status === 200) {
                return response.data; 
            }
        } catch (error: any) {
            throw error.response.data; 
        }
    }
}
