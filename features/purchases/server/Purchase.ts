import axios from "axios";
import <PERSON>zi<PERSON><PERSON> from "@/lib/endpoints";
import { FetchPurchaseRequest } from "../types/purchases";

export class Purchases {
    private static token = localStorage.getItem("izi_token");

    public static async getAllPurchases(request: FetchPurchaseRequest) {
        try {
            const response = await axios.get(IziApi.purchases, {
                params: request,
                headers: {
                    Authorization: `Bearer ${this.token}`,
                },
            });

            if (response.status === 200) {
                console.log(response);
                return response.data; 
            }
        } catch (error: unknown) {
            if (axios.isAxiosError(error) && error.response) {
                throw error.response.data;
            }
            throw error;
        }
    }
}
