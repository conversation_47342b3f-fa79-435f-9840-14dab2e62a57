
import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Edit, Loader2, MoreHorizontal, Trash } from "lucide-react";
import React from "react";
import { InventoryCategory } from "../types/inventoryCategories";

type Props = {
  category: InventoryCategory;
};

export const InventoryCategoryTableActions = ({ category }: Props) => {

  
  // const router = useRouter();
  
  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="ghost" className="h-8 w-8 p-0">
          <span className="sr-only">Open menu</span>
          <MoreHorizontal />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end">
        <DropdownMenuLabel>Actions</DropdownMenuLabel>
        <DropdownMenuSeparator />
        <DropdownMenuGroup>
          {/* <DropdownMenuItem className="flex" onClick={() => router.push(`/products/${category.id}?type=category`)}>
            <Eye /> View
          </DropdownMenuItem> */}
          <DropdownMenuItem
             onClick={() => {}}
            >
            <Edit /> Edit
          </DropdownMenuItem>
          <DropdownMenuSeparator />
          <DropdownMenuItem 
          onClick={() => {
            // deleteInventoryCategory.mutate(category.id.toString())
          }}
          ><Trash color="red" /> Delete 
           {/* {deleteInventoryCategory.isPending && <Loader2 className="spin"/> } */}
          </DropdownMenuItem>
        </DropdownMenuGroup>
      </DropdownMenuContent>
    </DropdownMenu>
  );
};