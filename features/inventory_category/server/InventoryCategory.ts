import axios from "axios";
import <PERSON>zi<PERSON><PERSON> from "@/lib/endpoints";
import { FetchInventoryCategoryRequest } from "../types/inventoryCategories";

export class InventoryCategory {
    private static token = localStorage.getItem("izi_token");

    public static async getAllInventoryCategories(request: FetchInventoryCategoryRequest) {
        try {
            const response = await axios.get(IziApi.inventoryCategories, {
                params: request,
                headers: {
                    Authorization: `Bearer ${this.token}`,
                },
            });

            if (response.status === 200) {
                console.log(response);
                return response.data; 
            }
        } catch (error: unknown) {
            if (axios.isAxiosError(error) && error.response) {
                throw error.response.data;
            }
            throw error;
        }
    }
}
