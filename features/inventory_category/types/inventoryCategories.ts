
export interface InventoryCategory {
    id: number;
    name: string;
    description: string;
    
    created_at: string;
    updated_at: string;
    deleted_at: string;
}

//Fetch 
export type FetchInventoryCategoryRequest = {
    query?: string;
    with_trashed?: boolean;
    page?: string;
    organization_id?: string;
    search?: string;
}

export type FetchInventoryCategoryResponse = {
    data: InventoryCategory[];
    total: number;
}

// Delete category
export interface DeleteInventoryCategoryResponse {
    data?: InventoryCategory;
  }